'use server';

import Link from "next/link";
import { Breadcrumbs, Typography } from "@mui/material";
import { PermissionGuard } from "@/_core/ui/components/permissonGuard/permissionGuard";
import { PostCreateRenderer } from "@/_core/ui/components/CRUD/postCreate/postCreateRenderer";
import { metaCreateBody, stepsBody } from "./meta/metaCreateBody";

const ENTITY = "obras_sociales";
const ACTION = "create";

export default async function Dashboard() {

  return (
    <div className="p-6 space-y-4">
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb">
        <Link
          className="hover:underline"
          href="/dashboard"
          color="inherit"
        >
          Home
        </Link>
        <Link
          className="hover:underline"
          href="/obras_sociales"
          color="inherit"
        >
          Obras Sociales
        </Link>
        <Typography color="inherit" className="pointer-events-none select-none">Nuevo</Typography>
      </Breadcrumbs>
      {/* Formulario */}
      <PermissionGuard entity={ENTITY} action={ACTION} fallback={<></>}>
        <PostCreateRenderer
          entityName={ENTITY}
          actionName={ACTION}
          meta={metaCreateBody}
          steps={stepsBody}
          label="Nueva Obra Social"
        />
      </PermissionGuard>
    </div>
  );
}
