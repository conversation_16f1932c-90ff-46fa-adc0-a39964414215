
import { FieldMeta } from "@/_core/ui/components/forms/types";
import { InferSchema } from "@/_lib/data/model/schema";
import { Step } from "@/_core/ui/components/forms/types";

export const metaCreateBody: Record<
  keyof InferSchema<"obras_sociales", "create">,
  FieldMeta
> = {
  denominacion: {
    label: "Denominación",
    type: "text",
    grid: { xs: 12, md: 12, lg: 12, xl: 12 },
  },
  fechaAlta: {
    label: "Fecha de alta",
    type: "date",
    grid: { xs: 12, md: 12, lg: 12, xl: 12 },
  },
  estadoId: {
    label: "Estado",
    type: "asyncSelect",
    entity: "estados_obras_sociales",
    labelKeys: ["nombre"],
    valueKey: "id",
    grid: { xs: 12, md: 12, lg: 12, xl: 12 },
  },
  domicilioFacturacion: {
    type: "group",
    label: "Domicilio de facturación",
    fields: {
      calle: {
        label: "Calle",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      numero: {
        label: "Número",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      piso: {
        label: "Piso (opcional)",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      depto: {
        label: "Dpto (opcional)",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      descripcion: {
        label: "Descripción (opcional)",
        type: "text",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
      localidadId: {
        label: "Localidad",
        type: "asyncSelect",
        entity: "localidades",
        labelKeys: ["codigoPostal", "nombre", "partido.nombre"],
        valueKey: "id",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
      telefonoCodigoArea: {
        label: "Código de área",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      telefonoNumero: {
        label: "Número de teléfono",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      email: {
        label: "Email",
        type: "text",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
    },
  },
  domicilioLegal: {
    type: "group",
    label: "Domicilio legal",
    fields: {
      calle: {
        label: "Calle",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      numero: {
        label: "Número",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      piso: {
        label: "Piso (opcional)",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      depto: {
        label: "Dpto (opcional)",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      descripcion: {
        label: "Descripción (opcional)",
        type: "text",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
      localidadId: {
        label: "Localidad",
        type: "asyncSelect",
        entity: "localidades",
        labelKeys: ["codigoPostal", "nombre", "partido.nombre"],
        valueKey: "id",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
      telefonoCodigoArea: {
        label: "Código de área",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      telefonoNumero: {
        label: "Número de teléfono",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      email: {
        label: "Email",
        type: "text",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
    },
  },
  contactos: {
    type: "group",
    label: "Contactos",
    fields: {
      descripcion: {
        label: "Descripción (opcional)",
        type: "text",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
      codigoArea: {
        label: "Código de área",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      telefono: {
        label: "Número de teléfono",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      email: {
        label: "Email",
        type: "text",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
    },
  },
};

export const stepsBody: Step<"obras_sociales", "create">[] = [
  {
    label: "Datos de la obra social",
    fields: ["denominacion", "fechaAlta", "estadoId", "domicilioFacturacion", "domicilioLegal"],
  },
  {
    label: "Contactos",
    fields: ["contactos"],
  },
];
