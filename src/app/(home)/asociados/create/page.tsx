"use server";

import Link from "next/link";
import { Breadcrumbs, Typography } from "@mui/material";
import { PermissionGuard } from "@/_core/ui/components/permissonGuard/permissionGuard";
import { PostCreateRenderer } from "@/_core/ui/components/CRUD/postCreate/postCreateRenderer";
import { InferSchema } from "@/_lib/data/model/schema";

import { metaCreateBody, stepsBody } from "./meta/metaCreateBody";

const ENTITY = "asociados";
const ACTION = "create";

export default async function Dashboard() {

  // Valores por defecto para campos requeridos
  const defaultValues = {
    domicilio: {
      fechaDesde: new Date().toISOString().split('T')[0], // Fecha actual
      activo: true, // Por defecto activo
    },
    matricula: {
      fechaAlta: new Date().toISOString().split('T')[0], // Fecha actual
    },
    habilitaciones:[]
  } as Partial<InferSchema<typeof ENTITY, typeof ACTION>>;

  return (
    <div className="p-6 space-y-4">
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb">
        <Link className="hover:underline" href="/dashboard" color="inherit">
          Home
        </Link>
        <Link className="hover:underline" href="/asociados" color="inherit">
          Asociados
        </Link>
        <Typography color="inherit" className="pointer-events-none select-none">
          Nuevo
        </Typography>
      </Breadcrumbs>

      {/* Formulario */}
      <PermissionGuard entity="asociados" action="create" fallback={<></>}>
        <PostCreateRenderer
          entityName={ENTITY}
          actionName={ACTION}
          meta={metaCreateBody}
          steps={stepsBody}
          defaultValues={defaultValues}
          label="Nuevo Socio"
        />
      </PermissionGuard>
    </div>
  );
}
