import React, { Suspense } from "react";
import ReadCollectionRenderer from "@/_core/ui/components/CRUD/readCollection/readCollectionRenderer";
import { Typography, Stack } from "@mui/material";
import CreateButton from "@/_core/ui/components/button/createButton";
import { DataRow } from "@/_core/utils/crud";
import { ColumnConfigWithStringRenderer } from "@/_core/ui/components/CRUD/readCollection/readCollectionRenderer";

const ENTITY = "asociados";
const ACTION = "collection";

const columnsConfig: ColumnConfigWithStringRenderer[] = [
  {
    key: "profesional",
    label: "PROFESIONAL",
    renderer: "navigate",
    options: {
      basePath: "/asociados",
      paramKey: "id",
    },
  },
  { key: "matricula", label: "MATRICULA", renderer: "integer" },
  { key: "localidad", label: "LOCALIDAD", renderer: "default" },
  { key: "cuit", label: "CUIT", renderer: "default" },
  {
    key: "estado",
    label: "ESTADO",
    renderer: "badge",
    options: {
      bg: "bg-gray-100",
      text: "text-gray-700",
    },
  },
  { key: "__actions", label: "", renderer: "actions" },
];

async function reduceAction(row: DataRow) {
  "use server"
  if(!row) return {};

  const domicilio = row.domicilio as Record<string, unknown>;
  const localidad = domicilio && domicilio.localidad ? (domicilio.localidad as Record<string, unknown>) : {};
  const matriculas = Array.isArray(row.matriculas) ? row.matriculas as Record<string, unknown>[] : [];
  const matricula = matriculas[0] as Record<string, unknown>;
  const estadoMatricula = matricula.estadoActual ? (matricula.estadoActual as { estado: Record<string, unknown> }) : { estado: {} };
  const perfilFiscal = row.perfilFiscal as Record<string, unknown>;

  return {
    id: row.id,
    profesional: `${row.apellido}, ${row.nombre}`.toUpperCase(),
    matricula: matricula.numero ?? "",
    localidad: localidad.nombre ?? "",
    cuit: perfilFiscal?.cuit ?? "",
    estado: estadoMatricula.estado ? estadoMatricula.estado.nombre ?? "" : "",
    __actions: [
      {
        key: "view",
        renderer: "view",
        options: {
          entityName: ENTITY,
          entityId: row.id,
        },
      },
      {
        key: "delete",
        renderer: "delete",
        options: {
          entityName: ENTITY,
          entityId: row.id,
          entityLabel: `${row.apellido}, ${row.nombre}`.toUpperCase(),
        },
      },
    ],
  };
}

export default async function AsociadosPage() {

  return (
    <main>
      <Stack direction="row" justifyContent="space-between">
        <Typography
          sx={{ typography: { xs: "h6", md: "h6xl", lg: "h2xl" } }}
          className="pl-4"
          color="text.primary"
        >
          Asociados
        </Typography>
        <CreateButton entityName="asociados" />
      </Stack>
      <Suspense fallback={<div>Cargando...</div>}>
        <ReadCollectionRenderer entityName={ENTITY} actionName={ACTION} columnsConfig={columnsConfig} reduceAction={reduceAction} />
      </Suspense>
    </main>
  );
}
