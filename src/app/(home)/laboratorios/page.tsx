"use server";

import React, { Suspense } from "react";
import ReadCollectionRenderer, { ColumnConfigWithStringRenderer } from "@/_core/ui/components/CRUD/readCollection/readCollectionRenderer";
import { Typography, Stack } from "@mui/material";
import CreateButton from "@/_core/ui/components/button/createButton";
import { DataRow } from "@/_core/utils/crud";

const ENTITY = "laboratorios";
const ACTION = "collection";



const columnsConfig: ColumnConfigWithStringRenderer[] = [
  {
    key: "razon_social",
    label: "RAZON SOCIAL",
    renderer: "navigate",
    options: {
      basePath: "/laboratorios",
      paramKey: "id",
    },
  },
  { key: "codigo", label: "CODIGO", renderer: "default" },
  { key: "cuit", label: "CUIT", renderer: "default" },
  { key: "iva", label: "IVA", renderer: "default" },
  { key: "localidad", label: "LOCALIDAD", renderer: "default" },
  { key: "__actions", label: "", renderer: "actions" },
];

export default async function LaboratoriosPage() {
  async function reduceAction(row: DataRow) {
    "use server";
    if (!row) return {};
    
    const domicilio = row.domicilio as Record<string, unknown>;
    const localidad = domicilio && domicilio.localidad ? (domicilio.localidad as Record<string, unknown>) : {};
    const perfilFiscal = row.perfilFiscal as Record<string, unknown>;
    console.log(perfilFiscal);
    
    return {
      id: row.id,
      razon_social: row.razonSocial ?? "",
      codigo: row.codigo ?? "",
      cuit: perfilFiscal?.cuit ?? "",
      iva: row.iva ?? "",
      localidad: localidad.nombre ?? "",
      __actions: [
        {
          key: "view",
          renderer: "view",
          options: {
            entityName: ENTITY,
            entityId: row.id,
          },
        },
        {
          key: "delete",
          renderer: "delete",
          options: {
            entityName: ENTITY,
            entityId: row.id,
            entityLabel: row.razonSocial ?? "",
          },
        },
      ],
    };
  }

  return (
    <main>
      <Stack direction="row" justifyContent="space-between">
        <Typography
          sx={{ typography: { xs: "h6", md: "h6xl", lg: "h2xl" } }}
          className="pl-4"
          color="text.primary"
        >
          Laboratorios
        </Typography>
        <CreateButton entityName={ENTITY} />
      </Stack>
      <Suspense fallback={<div>Cargando...</div>}>
        <ReadCollectionRenderer
          entityName={ENTITY}
          actionName={ACTION}
          columnsConfig={columnsConfig}
          reduceAction={reduceAction}
        />
      </Suspense>
    </main>
  );
}
