
import { EntityKeys } from "@/_lib/utils/entities";
import { IconKey } from "@/_ui/icons/iconMap";

export type AvailableRenderers = "delete | default";

export type EntityRendererOptions = {
  entityName: EntityKeys;
  entityId: string;
};

/**
 * Options for action rendering
 */
export type RendererOptions = Record<string, unknown>;

/**
 * Action metadata
 */
export type ActionMeta = {
  key: string;
  icon?: IconKey;
  renderer: AvailableRenderers;
  options: RendererOptions & EntityRendererOptions;
};