"use client";

import { ReactNode, useRef } from "react";
import {
  useForm,
  FormProvider,
  FieldValues,
  DefaultValues,
} from "react-hook-form";
import { z, ZodTypeAny } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";

import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import "dayjs/locale/es";
import dayjs from "dayjs";

import { FormSchemaContext } from "./hooks/useFormSchema";

import { runFormAction } from "@/_lib/data/model/action/actionFactory";
import { EntityKeys } from "@/_lib/utils/entities";
import { ServerActionParams } from "@/_lib/data/model/action/actionFactory";

export type MutationVariables = {
  formValues: Record<
    string,
    | string
    | number
    | boolean
    | Record<string, unknown>
    | Record<string, unknown>[]
    | null
  >;
  entityName: EntityKeys;
  actionName: string;
};

type FormContextProviderProps<
  Schema extends ZodTypeAny,
  FormValues extends FieldValues = z.infer<Schema>
> = {
  schema: Schema;
  children: ReactNode;
  defaultValues?: DefaultValues<FormValues>;
  action?: (formData: ServerActionParams) => Promise<unknown>;
  entityName: EntityKeys;
  actionName: string;
};

export function FormContextProvider<
  Schema extends ZodTypeAny,
  FormValues extends FieldValues = z.infer<Schema>
>({
  schema,
  children,
  defaultValues,
  action,
  entityName,
  actionName,
}: FormContextProviderProps<Schema, FormValues>) {
  const methods = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: "onChange",
  });

  // Ref para prevenir dobles ejecuciones
  const isSubmittingRef = useRef(false);

  const mutation = useMutation<void, Error, MutationVariables>({
    mutationKey: [entityName, actionName],
    mutationFn: async ({ formValues }) => {
      const payload: ServerActionParams = { body: formValues };
      if (action) {
        await action(payload);
      } else {
        await runFormAction(entityName, actionName, payload);
      }
    },
    // Prevenir múltiples ejecuciones simultáneas
    retry: false,
  });

  /**
   * Serializa valores del formulario para enviar al backend
   */
  const toSerializable = (value: unknown): string | number | boolean | Record<string, unknown> | Record<string, unknown>[] | null => {
    if (value == null) return null;

    // Date
    if (value instanceof Date) {
      return value.toISOString();
    }

    // dayjs
    if (dayjs.isDayjs(value)) {
      return value.toDate().toISOString();
    }

    const t = typeof value;

    // Primitivos
    if (t === "string" || t === "number" || t === "boolean") {
      return value as string | number | boolean;
    }

    // Array
    if (Array.isArray(value)) {
      return value.map(v => toSerializable(v)) as Record<string, unknown>[];
    }

    // Objetos
    if (t === "object") {
      const entries = Object.entries(value as Record<string, unknown>)
         .reduce((acc, [k, v]) => ({ ...acc, [k]: toSerializable(v) }), {} as Record<string, unknown>);
       return entries;
    }

    // Fallback
    return String(value);
  };

  const onSubmit = async (values: FormValues) => {
    // Prevenir dobles ejecuciones
    if (isSubmittingRef.current) {
      console.warn("Formulario ya está siendo enviado, ignorando ejecución duplicada");
      return;
    }

    try {
      isSubmittingRef.current = true;

      const processedValues: MutationVariables["formValues"] = {};

      for (const [key, value] of Object.entries(values as Record<string, unknown>)) {
        processedValues[key] = toSerializable(value);
      }

      await mutation.mutateAsync({ formValues: processedValues, entityName, actionName });
    } finally {
      isSubmittingRef.current = false;
    }
  };

  return (
    <FormSchemaContext.Provider value={schema}>
      <FormProvider {...methods}>
        <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="es">
          <form onSubmit={methods.handleSubmit(onSubmit)}>{children}</form>
        </LocalizationProvider>
      </FormProvider>
    </FormSchemaContext.Provider>
  );
}
