"use client";

import React, { useState, useMemo, useEffect } from "react";
import { useFormContext, FieldValues, Path } from "react-hook-form";
import {
  FormControl,
  TextField,
  Autocomplete,
  CircularProgress,
  FormHelperText,
} from "@mui/material";
import { useInfiniteQuery } from "@tanstack/react-query";

import { useFieldError } from "../hooks/useFieldError";
import { FieldMeta, SelectOption } from "../types";
import { ErrorMessageRenderer } from "./errorMessageRenderer";

import { runFormAction } from "@/_lib/data/model/action/actionFactory";
import { EntityKeys } from "@/_lib/utils/entities";

type AsyncSelectFieldRendererProps<T extends FieldValues> = {
  name: Path<T>;
  config: FieldMeta;
};

// Type for the API response page
type ApiResponsePage = {
  data: SelectOption[];
  hasNextPage: boolean;
};

// Function to map API response to SelectOption
const mapToSelectOptions = (data: Record<string, unknown>[], labelKeys: string[], valueKey: string): SelectOption[] => {
  return data.map((item: Record<string, unknown>) => {
    // Handle different possible field names for label
    let label = "";
    if(labelKeys.length === 0) {
      label = String(item.id ?? "Sin nombre");
    } else {
      for (const key of labelKeys) {
        if (item[key]) {
          label += item[key] + " - ";
        }
      }
      label = label.slice(0, -3);
    }
    
    // Handle value
    const value = item[valueKey] ?? item.id ?? item.key ?? "";

    return { label, value: String(value) };
  });
};

export default function AsyncSelectFieldRenderer<T extends FieldValues>({
  name,
  config,
}: AsyncSelectFieldRendererProps<T>) {
  const { watch, setValue } = useFormContext<T>();
  const fieldValue = watch(name);
  const fieldError = useFieldError<T>(name);
  
  const [inputValue, setInputValue] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  
  // Extract configuration with defaults
  const labelKeys = config.labelKeys ?? [];
  const valueKey = config.valueKey ?? "id"; // Default to "id"
  const debounceDelay = 300;
  const pageSize = 10;
  const searchField = config.searchField ?? "search";
  
  // Validate entity
  const entity = config.entity as EntityKeys;
  
  // Use infinite query for pagination
  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
  } = useInfiniteQuery<ApiResponsePage, Error>({
    queryKey: ["asyncSelect", entity, searchTerm],
    queryFn: async ({ pageParam = 1 }) => {
      if (!entity) {
        return { data: [], hasNextPage: false };
      }
      
      try {
        // Prepare params for the API call
        const query: Record<string, string | number> = {
          page: pageParam as number,
          limit: pageSize,
        };
        
        // Add search parameter if provided
        if (searchTerm) {
          query[searchField] = searchTerm;
        }
        
        // Call the server action
        const result = await runFormAction(entity, "collection", {
          query: query,
        });
        
        // Process the response
        if (result && typeof result === "object" && "data" in result) {
          const apiData = result.data;
          if (Array.isArray(apiData)) {
            const options = mapToSelectOptions(apiData, labelKeys, valueKey);
            return {
              data: options,
              hasNextPage: apiData.length === pageSize,
            };
          }
        }
        
        return { data: [], hasNextPage: false };
      } catch (err) {
        console.error("Error fetching options:", err);
        throw new Error("Error al cargar las opciones");
      }
    },
    getNextPageParam: (lastPage, pages) => {
      return lastPage.hasNextPage ? pages.length + 1 : undefined;
    },
    initialPageParam: 1,
    enabled: !!entity,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes (cacheTime was renamed to gcTime in newer versions)
  });
  
  // Flatten the paginated data
  const allOptions = useMemo(() => {
    if (!data) return [];
    return data.pages.flatMap(page => page.data);
  }, [data]);
  
  // Find the selected option
  const selectedOption = useMemo(() => {
    if (fieldValue === null || fieldValue === undefined || fieldValue === "") return null;
    return allOptions.find(option => {
      // Handle both string and number comparisons
      return String(option.value) === String(fieldValue);
    }) || null;
  }, [allOptions, fieldValue]);

  // Keep inputValue in sync with selectedOption
  useEffect(() => {
    if (selectedOption) {
      setInputValue(selectedOption.label);
    } else if (fieldValue === null || fieldValue === undefined || fieldValue === "") {
      setInputValue("");
    }
  }, [selectedOption, fieldValue]);
  
  // Handle input change with debounce
  const handleInputChange = useMemo(() => {
    let timeoutId: NodeJS.Timeout;

    return (value: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setSearchTerm(value);
      }, debounceDelay);
    };
  }, [debounceDelay]);
  
  return (
    <FormControl fullWidth error={!!fieldError || !!error}>
      <Autocomplete
        value={selectedOption}
        onChange={(_, newValue) => {
          // Convert to number if the field name suggests it's an ID field
          const value = newValue ? newValue.value : null;
          const finalValue = name.toLowerCase().includes('id') && value ? Number(value) : value;
          setValue(name, finalValue as T[Path<T>], { shouldValidate: true });
        }}
        inputValue={inputValue}
        onInputChange={(_, newInputValue, reason) => {
          setInputValue(newInputValue);
          if (reason === 'input') {
            handleInputChange(newInputValue);
          }
        }}
        onBlur={() => {
          // When losing focus, restore the selected option's label if there's a selection
          if (selectedOption) {
            setInputValue(selectedOption.label);
          }
        }}
        options={allOptions}
        getOptionLabel={(option) => (typeof option === 'string' ? option : option.label)}
        isOptionEqualToValue={(option, value) => option.value === value.value}
        loading={isFetching && !isFetchingNextPage}
        loadingText="Cargando..."
        noOptionsText="No se encontraron opciones"
        openText="Abrir"
        closeText="Cerrar"
        clearText="Limpiar"
        size={config.size ?? "small"}
        renderInput={(params) => (
          <TextField
            {...params}
            label={config.label}
            placeholder={`Buscar ${config.label ?? ""}`}
            variant={config.variant ?? "outlined"}
            error={!!fieldError || !!error}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <>
                  {isFetching && !isFetchingNextPage ? <CircularProgress color="inherit" size={20} /> : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            }}
          />
        )}
        ListboxProps={{
          onScroll: (event) => {
            const listboxNode = event.currentTarget;
            if (
              listboxNode.scrollTop + listboxNode.clientHeight >=
              listboxNode.scrollHeight - 5 &&
              hasNextPage &&
              !isFetchingNextPage
            ) {
              fetchNextPage();
            }
          },
        }}
      />
      {error && (
        <FormHelperText error>{error instanceof Error ? error.message : "Error desconocido"}</FormHelperText>
      )}
      <ErrorMessageRenderer error={fieldError} />
    </FormControl>
  );
}
