import { InferSchema, <PERSON>tityK<PERSON><PERSON>, ActionKeys } from "@/_lib/data/model/schema";

export type FieldType = "text" | "email" | "number" | "checkbox" | "password" | "username" | "integer" | "date" | "select" | "group" | "asyncSelect" | "title";

export type SelectOption = {
  label: string;
  value: string | number;
};

export type FieldGrid = {
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
};

export type FieldMeta = {
  label?: string;
  type: FieldType;
  required?: boolean;
  className?: string;
  size?: "small" | "medium";
  variant?: "filled" | "outlined" | "standard";
  color?: "primary" | "secondary" | "error" | "info" | "success" | "warning";
  sx?: Record<string, unknown>;
  grid?: FieldGrid;
  options?: SelectOption[];
  fields?: Record<string, FieldMeta>;
  entity?: string;
  searchField?: string; // Campo de búsqueda para asyncSelect
  labelKeys?: string[]; // Claves para el label de asyncSelect
  valueKey?: string; // Clave para el valor de asyncSelect
};

export type Step<E extends EntityKeys, A extends ActionKeys<E>> = {
  label: string;
  fields: (Extract<keyof InferSchema<E, A>, string>)[];
};
