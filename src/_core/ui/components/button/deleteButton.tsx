"use client";

import React, { useState } from "react";
import { Stack, ListItemIcon, ListItemText } from "@mui/material";
import { Trash2 } from "react-feather";
import DeleteConfirmModal from "../CRUD/delete/deleteConfirmModalRenderer";

import { EntityKeys } from "@/_lib/utils/entities";

export default function DeleteButton({
  entityName,
  entityId,
  entityLabel,
  size = "small",
  onDeleteSuccess,
  onMenuClose,
}: {
  entityName: EntityKeys;
  entityId: string;
  size?: "small" | "medium" | "large";
  entityLabel?: string;
  onDeleteSuccess?: () => void;
  onMenuClose?: () => void;
}) {
  const [open, setOpen] = useState(false);

  const handleOpen = (event: React.MouseEvent) => {
    event.preventDefault(); // Prevenir el comportamiento por defecto
    event.stopPropagation(); // Detener la propagación del evento
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    // Cerrar el menú padre cuando se cierre el modal
    if (onMenuClose) {
      onMenuClose();
    }
  };

  const iconSize = size === "small" ? 18 : size === "medium" ? 20 : 24;

  return (
    <>
      {/* Trigger Button */}
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        onClick={handleOpen}
      >
        <ListItemIcon>
          <Trash2 size={iconSize} />
        </ListItemIcon>
        <ListItemText>Eliminar</ListItemText>
      </Stack>

      {/* Confirmation Modal */}
      <DeleteConfirmModal
        open={open}
        onClose={handleClose}
        entityName={entityName}
        entityId={entityId}
        entityLabel={entityLabel}
        onDeleteSuccess={onDeleteSuccess}
      />
    </>
  );
}
