"use client";

import Link from "next/link";
import {
  ListItemIcon,
  ListItemText,
  Stack,
} from "@mui/material";
import { Eye } from "react-feather";

export default function ViewButton({
  entityId,
  entityName,
  size = "small",
}: {
  entityId: string;
  entityName: string;
  size?: "small" | "medium" | "large";
}) {
  const iconSize = size === "small" ? 18 : size === "medium" ? 20 : 24;
  return (
    <Link href={`/${entityName}/${entityId}`}>
        <Stack
        direction="row"
        alignItems="center"
        spacing={2}
      >
        <ListItemIcon>
            <Eye size={iconSize} />
        </ListItemIcon>
        <ListItemText>Ver</ListItemText>
      </Stack>
    </Link>
  );
}
