"use client";

import React, { useState } from "react";
import { Stack, Chip, Box, TextField, InputAdornment } from "@mui/material";
import { X, Search } from "react-feather";
import FiltersButton from "./filtersButton";

import { useSearchParams, useRouter, usePathname } from "next/navigation";

export default function Filters() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const query = Object.fromEntries(searchParams.entries());
  const [searchValue, setSearchValue] = useState("");

  // Filtrar parámetros de paginación
  const paginationParams = ["page", "limit"];
  const filterParams = Object.entries(query).filter(
    ([key]) => !paginationParams.includes(key)
  );

  const handleRemoveFilter = (keyToRemove: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete(keyToRemove);
    router.push(`${pathname}?${params.toString()}`);
  };

  const handleSearchSubmit = (value: string) => {
    if (value.trim()) {
      const params = new URLSearchParams(searchParams.toString());
      params.set("search", value.trim());
      // Resetear página al hacer una nueva búsqueda
      params.delete("page");
      router.push(`${pathname}?${params.toString()}`);
      setSearchValue("");
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      handleSearchSubmit(searchValue);
    }
  };

  return (
    <Stack direction="row" justifyContent={"space-between"} alignItems="center" spacing={16}>
      <Box
        className="border-1 border-gray-300 rounded-lg p-2 w-full"
        sx={{ display: "flex", flexWrap: "wrap", gap: 1, alignItems: "center" }}
      >
        <TextField
          size="small"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onKeyDown={handleKeyDown}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <Search size={20} />
                </InputAdornment>
              ),
            },
          }}
          sx={{
            flex: 1,
            minWidth: "200px",
            "& .MuiOutlinedInput-root": {
              "& fieldset": {
                border: "none",
              },
            },
          }}
        />
        {filterParams.map(([key, value]) => (
          <Chip
            key={key}
            label={`${key}: ${value}`}
            onDelete={() => handleRemoveFilter(key)}
            deleteIcon={<X size={16} />}
            variant="outlined"
            color="primary"
          />
        ))}
      </Box>
      <FiltersButton onEdit={() => {}} />
    </Stack>
  );
}
