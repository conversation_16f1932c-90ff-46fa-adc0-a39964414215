
"use client";

import React from "react";
import { Pagination as MuiPagination } from "@mui/material";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";

type Props = {
  count: number;
  page: number;
  color: "primary" | "secondary";
  className: string;
};

export default function Pagination({ count, page, color, className }: Props) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", value.toString());
    router.push(`?${params.toString()}`);
  };

  return (
    <MuiPagination
      count={count}
      page={page}
      color={color}
      className={className}
      onChange={handlePageChange}
    />
  );
}
