"use client";

import React from "react";
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Stack,
} from "@mui/material";
import { renderers } from "../renderers";
import Pagination from "../pagination/pagination";
import Filters from "@/_core/ui/components/CRUD/readCollection/filters/filters";
import { ColumnConfig } from "../readCollectionRenderer";
import { DataRow } from "@/_core/utils/crud";

type Props = {
  data: DataRow[];
  columnsConfig?: ColumnConfig[];
  isLoading?: boolean;
  isError?: boolean;
  meta?: {
    itemsPerPage: number;
    totalItems: number;
    currentPage: number;
    totalPages: number;
  };
};

export default function PageVariant({ data, columnsConfig, isLoading, isError, meta }: Props) {
  const columns: ColumnConfig[] = columnsConfig
    ? columnsConfig
    : Object.keys(data[0])
        .filter((col) => col !== "__actions")
        .map((key) => ({ key, renderer: renderers.default }));

  // Para saber si una columna es oculta (renderer === hidden)
  const isVisible = (col: ColumnConfig) => col.renderer !== renderers.hidden;

  return (
    <Paper elevation={3} className="mt-7 p-8">
      <Stack direction="column" spacing={2}>
        <Filters />
        <TableContainer>
          <Table sx={{ border: "2px solid #F2F4F8" }}>
            <TableHead sx={{ bgcolor: "primary.main" }}>
              <TableRow>
                {columns.filter(isVisible).map(({ key, label }) => (
                  <TableCell key={key} sx={{ color: "primary.contrastText" }}>
                    <Typography
                      className="font-bold"
                      sx={{
                        typography: {
                          xs: "body3",
                          md: "body3xl",
                          lg: "body2xl",
                        },
                      }}
                    >
                      {label ?? key}
                    </Typography>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {data.length === 0 && !isLoading && !isError && (
                <TableRow>
                  <TableCell colSpan={columns.filter(isVisible).length}>
                    <Typography
                      className="text-center"
                      sx={{
                        typography: {
                          xs: "body3",
                          md: "body3xl",
                          lg: "body2xl",
                        },
                      }}
                    >
                      No hay datos para mostrar
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
              {isLoading && (
                <TableRow>
                  <TableCell colSpan={columns.filter(isVisible).length}>
                    <Typography
                      className="text-center"
                      sx={{
                        typography: {
                          xs: "body3",
                          md: "body3xl",
                          lg: "body2xl",
                        },
                      }}
                    >
                      Cargando...
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
              {isError && (
                <TableRow>
                  <TableCell colSpan={columns.filter(isVisible).length}>
                    <Typography
                      className="text-center"
                      sx={{
                        typography: {
                          xs: "body3",
                          md: "body3xl",
                          lg: "body2xl",
                        },
                      }}
                    >
                      Error al cargar los datos
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
              {data.map((row, index) => (
                <TableRow
                  key={index}
                  sx={{ borderBottom: "2px solid #F2F4F8" }}
                >
                  {columns
                    .filter(isVisible)
                    .map(({ key, renderer, options }) => (
                      <TableCell key={key}>
                        {renderer
                          ? renderer(row[key], row, key, options)
                          : String(row[key] ?? "")}
                      </TableCell>
                    ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <Pagination
          count={meta?.totalPages || 1}
          page={meta?.currentPage || 1}
          color="primary"
          className="flex justify-end"
        ></Pagination>
      </Stack>
    </Paper>
  );
}
