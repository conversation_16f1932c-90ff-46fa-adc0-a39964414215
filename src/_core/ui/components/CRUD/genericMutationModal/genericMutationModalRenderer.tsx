"use client";

import { ClientFormWrapper } from "@/_core/ui/components/forms/clientFormWrapper";
import { FieldMeta } from "@/_core/ui/components/forms/types";
import { InferSchema, EntityKeys, ActionKeys } from "@/_lib/data/model/schema";
import { FormValidateAndSubmitButton } from "@/_core/ui/components/forms/formValidateAndSubmitButton";

import { Box, Typography, Stack, Modal, Button } from "@mui/material";

import { useState } from "react";

type PatchEditRendererProps<E extends EntityKeys, A extends ActionKeys<E>> = {
  entityName: E;
  entityId: string | number;
  actionName?: A;
  meta: Record<keyof InferSchema<E, A>, FieldMeta>;
  defaultValues?: Partial<InferSchema<E, A>>;
  label: string;
};

export function GenericMutationModalRenderer<
  E extends EntityKeys,
  A extends ActionKeys<E>
>({
  entityName,
  actionName = "edit" as A,
  defaultValues,
  meta,
  label,
}: PatchEditRendererProps<E, A>) {
    const [open, setOpen] = useState(false);
    const handleOpen = () => {
        setOpen(true);
    };
    const handleClose = () => {
        setOpen(false);
    };
  return (
    <Modal open={open}>
      <ClientFormWrapper
        entityName={entityName}
        actionName={actionName}
        meta={meta}
        defaultValues={defaultValues}
        header={
          <>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems={"center"}
              className="mb-1"
            >
              <Typography
                color="primary.main"
                sx={{ typography: { xs: "h5", md: "h5", lg: "h5xl" } }}
              >
                {label}
              </Typography>
            </Stack>
          </>
        }
      >
        <Stack direction="row" justifyContent="flex-end">
            <Button onClick={handleClose}>Cancelar</Button>
            <FormValidateAndSubmitButton label="Guardar" />
        </Stack>
      </ClientFormWrapper>
    </Modal>
  );
}
