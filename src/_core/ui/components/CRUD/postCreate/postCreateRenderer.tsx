"use client";

import { ClientFormWrapper } from "@/_core/ui/components/forms/clientFormWrapper";
import { FieldMeta } from "@/_core/ui/components/forms/types";
import { InferSchema, EntityKeys, ActionKeys } from "@/_lib/data/model/schema";
import { FormValidateAndSubmitButton } from "@/_core/ui/components/forms/formValidateAndSubmitButton";

import { Step } from "@/_core/ui/components/forms/types";

import { Box, Typography, Stack } from "@mui/material";

type CreateFormClientProps<E extends EntityKeys, A extends ActionKeys<E>> = {
  entityName: E;
  actionName: A;
  meta: Record<keyof InferSchema<E, A>, FieldMeta>;
  steps?: Step<E, A>[];
  defaultValues?: Partial<InferSchema<E, A>>;
  label: string;
};

export function PostCreateRenderer<
  E extends EntityKeys,
  A extends ActionKeys<E>
>({
  entityName,
  actionName,
  meta,
  steps,
  defaultValues,
  label,
}: CreateFormClientProps<E, A>) {
  return (
    <Box className="mt-6 md:mt-10 lg:mt-12 md:mx-2 lg:mx-0">
      <ClientFormWrapper
        entityName={entityName}
        actionName={actionName}
        meta={meta}
        steps={steps}
        defaultValues={defaultValues}
        header={(
          <>
          <Stack direction="row" justifyContent="space-between" alignItems={"center"} className="mb-1">
            <Typography
              color="primary.main"
              sx={{ typography: { xs: "h5", md: "h5", lg: "h5xl" } }}
            >
                {label}
            </Typography>
            <FormValidateAndSubmitButton label="Guardar" />
          </Stack>
          </>
        )}
      >
      </ClientFormWrapper>
    </Box>
  );
}
