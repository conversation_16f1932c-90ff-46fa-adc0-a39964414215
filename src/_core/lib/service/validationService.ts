import { BaseApiModel, SchemaKind } from "@/_core/lib/data/interface/base/baseApiModel";
import { SchemaValidationError } from "@/_core/lib/context/error/badRequestError";

/**
 * Validates API input using a model schema and returns the validated/masked data
 * @param model - API model with schemas
 * @param kind - Schema kind to validate against
 * @param input - Input data to validate
 * @returns Validated and masked data (same structure as input but validated)
 */
export function validateApiInput<T = unknown>(
  model: BaseApiModel,
  kind: SchemaKind,
  input: T
): T {
  const schema = model.getSchema(kind);
  const result = schema.validate(input);

  if (!result.success) {
    const err = result.error;
    const firstErrorName = err.issues[0].path[ err.issues[0].path.length - 1 ].toString();
    const firstError = err.issues[0].message;

    throw new SchemaValidationError(firstError, { atribute_name: firstErrorName });
  }

  return result.data as T;
}
