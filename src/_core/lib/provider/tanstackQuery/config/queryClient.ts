import { QueryClient } from '@tanstack/react-query'

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 1000 * 60 * 5,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: false, // No reintentar mutaciones fallidas automáticamente
      // Prevenir múltiples ejecuciones simultáneas de la misma mutación
      networkMode: 'online',
    },
  },
})
