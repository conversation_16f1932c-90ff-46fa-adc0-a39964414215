/**
 * Request genérica utilizando Axios como intermediario
 */

import axios, { AxiosResponse, Method, ResponseType, isAxiosError } from "axios";
import { FetchingError } from "@/_core/lib/context/error";

/**
 * Tipado estricto para config de axios.
 *
 */
export interface StrictHttpActionParams<TRequest = unknown> {
  url: string;
  method: Method;

  headers?: Record<string, string>;
  params?: Record<string, string | number | boolean>;
  data?: TRequest;

  timeout?: number;
  responseType?: ResponseType;
  auth?: {
    username: string;
    password: string;
  };
  baseURL?: string;
  withCredentials?: boolean;
}

export class BaseHttpRequest<TRequest = unknown, TResponse = unknown> {
  protected readonly config: StrictHttpActionParams<TRequest>;

  constructor(config: StrictHttpActionParams<TRequest>) {
    this.config = config;
  }

  async execute(): Promise<AxiosResponse<TResponse>> {
    try {
      return axios.request<TResponse>(this.config);
    } catch (error) {
      if (isAxiosError(error)) {
        throw new FetchingError(error);
      }
      throw error;
    }
  }
}
