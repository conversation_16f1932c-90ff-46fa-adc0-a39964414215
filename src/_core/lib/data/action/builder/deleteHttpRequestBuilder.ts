import { AxiosResponse } from "axios";
import { validateApiInput } from "@/_core/lib/service/validationService";
import { BaseHttpRequestBuilder } from "./baseHttpRequestBuilder";
import { BaseApiModel } from "@/_core/lib/data/interface/base/baseApiModel";
import { toQueryParams } from "@/_core/utils/typeConversion";

export class DeleteHttpRequestBuilder<TRequest = unknown, TResponse = unknown> extends BaseHttpRequestBuilder<
  TRequest,
  TResponse
> {
  constructor( url: string ) {
    super( url, "DELETE" );
    this.setBody({} as TRequest);
  }

  withValidation(model: BaseApiModel , fullMask?: boolean): this {
    this.addBeforeBuild(() => {
      const result = validateApiInput(model, "params", this["config"].params || {});
      if (fullMask) {
        this.setQueryParams(toQueryParams(result));
      }
    });

    this.addAfterExecute((response: AxiosResponse<TResponse>) => {
      const result = validateApiInput(model, "response", response.data);
      if (fullMask) {
        response.data = result as TResponse;
      }
    });

    return this;
  }
}
