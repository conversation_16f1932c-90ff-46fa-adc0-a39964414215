
"use server";

import { validateApiInput } from "@/_core/lib/service/validationService";
import { CollectionHttpRequestBuilder } from "@/_core/lib/data/action/builder";
import { EstadosLaboratoriosInterface } from "@/_lib/data/model/interface/";
import { getEntityConfig } from "@/_lib/utils/entities";
import { ServerActionParams } from "../actionFactory";

export async function submitEstadosLaboratoriosCollection( { query = {} }: ServerActionParams ) {
  "use server";

  const { meta } = getEntityConfig("estados_laboratorios", "collection");

  const builder = new CollectionHttpRequestBuilder(meta.url)
    .setQueryParams(query)
    .withValidation(EstadosLaboratoriosInterface.collectionEstadosLaboratoriosModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        EstadosLaboratoriosInterface.collectionEstadosLaboratoriosModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}
