
"use server";

import { validateApiInput } from "@/_core/lib/service/validationService";
import { CollectionHttpRequestBuilder } from "@/_core/lib/data/action/builder";
import { EstadosObrasSocialesInterface } from "@/_lib/data/model/interface/";
import { getEntityConfig } from "@/_lib/utils/entities";
import { ServerActionParams } from "@/_lib/data/model/action/actionFactory";

export async function submitEstadosObrasSocialesCollection( { query = {} }: ServerActionParams ) {
  "use server";

  const { meta } = getEntityConfig("estados_obras_sociales", "collection");

  const builder = new CollectionHttpRequestBuilder(meta.url)
    .setQueryParams(query)
    .withValidation(EstadosObrasSocialesInterface.collectionEstadosObrasSocialesModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        EstadosObrasSocialesInterface.collectionEstadosObrasSocialesModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}
