
import { ApiModelCollectionBuilder, ApiModelIndexBuilder, ApiModelCreateBuilder } from "@/_core/lib/data/interface/builder";
import { ENTITIES } from "@/_lib/config/entities";
import { getEntityKey, getActionKeys } from "@/_lib/utils/entities";
import { obrasSocialesSchemas } from "../schema/obras_sociales";

const { obras_sociales } = ENTITIES;

// Nombre de la entidad
export const NAME = getEntityKey(obras_sociales) as string;

// Acciones disponibles en la entidad
export const ACTION_NAMES = getActionKeys(obras_sociales) as (keyof typeof obrasSocialesSchemas)[];

// Modelos generados
export const indexObrasSocialesModel = new ApiModelIndexBuilder(ACTION_NAMES[0])
  .setResponse(obrasSocialesSchemas.index.response)
  .build();

export const collectionObrasSocialesModel = new ApiModelCollectionBuilder(ACTION_NAMES[1])
  .setFilters(obrasSocialesSchemas.collection.filters)
  .setResponseItemsSchema(obrasSocialesSchemas.collection.response)
  .build();

export const createObrasSocialesModel = new ApiModelCreateBuilder(ACTION_NAMES[2])
  .setBody(obrasSocialesSchemas.create.body)
  .setResponse(obrasSocialesSchemas.create.response)
  .build();
