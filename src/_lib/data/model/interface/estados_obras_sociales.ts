
import { ApiModelCollectionBuilder } from "@/_core/lib/data/interface/builder";
import { ENTITIES } from "@/_lib/config/entities";
import { getEntityKey, getActionKeys } from "@/_lib/utils/entities";
import { estadosObrasSocialesSchemas } from "../schema/estados_obras_sociales";

const { estados_obras_sociales } = ENTITIES;

// Nombre de la entidad
export const NAME = getEntityKey(estados_obras_sociales) as string;

// Acciones disponibles en la entidad
export const ACTION_NAMES = getActionKeys(estados_obras_sociales) as (keyof typeof estadosObrasSocialesSchemas)[];

// Modelos generados
export const collectionEstadosObrasSocialesModel = new ApiModelCollectionBuilder(ACTION_NAMES[0])
  .setFilters(estadosObrasSocialesSchemas.collection.filters)
  .setResponseItemsSchema(estadosObrasSocialesSchemas.collection.response)
  .build();
