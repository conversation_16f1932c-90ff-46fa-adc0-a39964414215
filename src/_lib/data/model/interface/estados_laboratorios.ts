
import { ApiModelCollectionBuilder } from "@/_core/lib/data/interface/builder";
import { ENTITIES } from "@/_lib/config/entities";
import { getEntityKey, getActionKeys } from "@/_lib/utils/entities";
import { estadosLaboratoriosSchemas } from "../schema/estados_laboratorios";

const { estados_laboratorios } = ENTITIES;

// Nombre de la entidad
export const NAME = getEntityKey(estados_laboratorios) as string;

// Acciones disponibles en la entidad
export const ACTION_NAMES = getActionKeys(estados_laboratorios) as (keyof typeof estadosLaboratoriosSchemas)[];

// Modelos generados
export const collectionEstadosLaboratoriosModel = new ApiModelCollectionBuilder(ACTION_NAMES[0])
  .setFilters(estadosLaboratoriosSchemas.collection.filters)
  .setResponseItemsSchema(estadosLaboratoriosSchemas.collection.response)
  .build();
