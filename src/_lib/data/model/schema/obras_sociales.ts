import { z } from "zod";
import { ENTITIES } from "@/_lib/config/entities";

export const ENTITY = ENTITIES.obras_sociales;

export const obrasSocialesSchemas = {
  index: {
    response: z.object({
      id: z.string().uuid(),
    }),
  },
  collection: {
    filters: z.object({
      search: z.string().optional().nullable(),
      page: z.number().int().min(1).default(1).optional().nullable(),
      limit: z.number().int().min(1).max(30).default(30).optional().nullable(),
    }),
    response: z.object({
      id: z.number().int(),
      denominacion: z.string(),
    }),
  },
  create: {
    body: z.object({
      denominacion: z
        .string()
        .min(3, "Ingrese por los menos 3 caracteres")
        .max(20, "No puede superar los 20 caracteres"),
      fechaAlta: z.string().date("La fecha debe ser una fecha válida"),
      estadoId: z.number().int(),
      domicilioFacturacion: z.object({
        calle: z
          .string()
          .min(3, "Ingrese por los menos 3 caracteres")
          .max(20, "No puede superar los 20 caracteres"),
        numero: z
          .string()
          .min(3, "Ingrese por los menos 3 caracteres")
          .max(20, "No puede superar los 20 caracteres"),
        piso: z.string().optional().nullable(),
        depto: z.string().optional().nullable(),
        descripcion: z.string().optional().nullable(),
        telefonoCodigoArea: z
          .string({
            errorMap: () => ({
              message: "El código de área es obligatorio",
            }),
          })
          .regex(
            /^\d{2,4}$/,
            "El código de área debe contener entre 2 y 4 dígitos"
          ),
        telefonoNumero: z
          .string({
            errorMap: () => ({
              message: "El número de teléfono es obligatorio",
            }),
          })
          .regex(
            /^\d{6,8}$/,
            "El número de teléfono debe contener entre 6 y 8 dígitos"
          ),
        email: z.string().email("El email debe ser un email válido"),
        localidadId: z.number().int(),
        referenciaId: z.number().int(),
      }),
      domicilioLegal: z.object({
        calle: z
          .string()
          .min(3, "Ingrese por los menos 3 caracteres")
          .max(20, "No puede superar los 20 caracteres"),
        numero: z
          .string()
          .min(3, "Ingrese por los menos 3 caracteres")
          .max(20, "No puede superar los 20 caracteres"),
        piso: z.string().optional().nullable(),
        depto: z.string().optional().nullable(),
        descripcion: z.string().optional().nullable(),
        telefonoCodigoArea: z
          .string({
            errorMap: () => ({
              message: "El código de área es obligatorio",
            }),
          })
          .regex(
            /^\d{2,4}$/,
            "El código de área debe contener entre 2 y 4 dígitos"
          ),
        telefonoNumero: z
          .string({
            errorMap: () => ({
              message: "El número de teléfono es obligatorio",
            }),
          })
          .regex(
            /^\d{6,8}$/,
            "El número de teléfono debe contener entre 6 y 8 dígitos"
          ),
        email: z.string().email("El email debe ser un email válido"),
        localidadId: z.number().int(),
        referenciaId: z.number().int(),
      }),
      contactos: z.object({
        descripcion: z.string().optional().nullable(),
        codigoArea: z
          .string({
            errorMap: () => ({
              message: "El código de área es obligatorio",
            }),
          })
          .regex(
            /^\d{2,4}$/,
            "El código de área debe contener entre 2 y 4 dígitos"
          ),
        telefono: z
          .string({
            errorMap: () => ({
              message: "El número de teléfono es obligatorio",
            }),
          })
          .regex(
            /^\d{6,8}$/,
            "El número de teléfono debe contener entre 6 y 8 dígitos"
          ),
        email: z.string().email("El email debe ser un email válido"),
        tipoId: z.number().int(),
      }),
    }),
    response: z.object({
      id: z.number().int(),
    }),
  },
} as const;
