
import { z } from "zod";
import { ENTITIES } from "@/_lib/config/entities";

export const ENTITY = ENTITIES.estados_laboratorios;

export const estadosLaboratoriosSchemas = {
  index: {
    response: z.object({
      id: z.string().uuid(),
    }),
  },
  collection: {
    filters: z.object({
      search: z.string().optional().nullable(),
      page: z.number().int().min(1).default(1).optional().nullable(),
      limit: z.number().int().min(1).max(30).default(30).optional().nullable(),
    }),
    response: z.object({
      id: z.number().int(),
      nombre: z.string(),
      key: z.string(),
    }),
  },
} as const;
