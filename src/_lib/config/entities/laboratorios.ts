import { EntityActions } from "../types";

export const LABORATORIOS_CONFIG: EntityActions = {
  collection: {
    description: "Listado de laboratorios",
    meta: {
      url: "/laboratorios",
      method: "GET",
      revalidates: {
        keys: [],
      },
    },
    permissions: ["public"],
  },
  index: {
    description: "Detalle de laboratorio",
    meta: {
      url: "/laboratorios/:id",
      method: "GET",
      revalidates: {
        keys: [],
      },
    },
    permissions: ["public"],
  },
  create: {
    description: "Creación de laboratorio",
    meta: {
      url: "/laboratorios",
      method: "POST",
      revalidates: {
        keys: ["/laboratorios"],
      },
    },
    permissions: ["public"],
  },
  delete: {
    description: "Baja de laboratorio",
    meta: {
      url: "/laboratorios/:id",
      method: "DELETE",
      revalidates: {
        keys: ["/laboratorios", "/laboratorios/:id"],
      },
    },
    permissions: ["public"],
  },
} as const;
