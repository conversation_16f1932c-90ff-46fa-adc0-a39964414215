import { EntityActions } from "../types";

export const ASOCIADOS_CONFIG: EntityActions= {
  collection: {
    description: "Listado de asociados",
    meta: {
      url: "/asociados",
      method: "GET",
      revalidates: {
        keys: [],
      }
    },
    permissions: ["colegio"],
  },
  index: {
    description: "Detalle de asociado",
    meta: {
      url: "/asociados/:id",
      method: "GET",
      revalidates: {
        keys: [],
      }
    },
    permissions: ["asociado","colegio"],
  },
  create: {
    description: "Creación de asociado",
    meta: {
      url: "/asociados",
      method: "POST",
      revalidates: {
        keys: ["/asociados"],
      }
    },
    permissions: ["public"],
  },
  edit: {
    description: "Edición de asociado",
    meta: {
      url: "/asociados/:id",
      method: "PATCH",
      revalidates: {
        keys: ["/asociados"],
      }
    },
    permissions: ["colegio"],
  },
  delete: {
    description: "Baja de asociado",
    meta: {
      url: "/asociados/:id",
      method: "DELETE",
      revalidates: {
        keys: ["/asociados", "/asociados/:id"],
      }
    },
    permissions: ["colegio"],
  },
} as const;